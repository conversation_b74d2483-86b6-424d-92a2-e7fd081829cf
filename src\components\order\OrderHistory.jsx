import { useState, useEffect } from "react";
import { formatToIST } from "../../utils/dateUtils";

export default function OrderHistory({ tab }) {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedOrderId, setSelectedOrderId] = useState(null);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [loadingOrderDetail, setLoadingOrderDetail] = useState(false);
  const [filterStatus, setFilterStatus] = useState(tab || "all");
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [cancellationReason, setCancellationReason] = useState("");
  const [cancellingOrder, setCancellingOrder] = useState(false);
  const [paymentTransactions, setPaymentTransactions] = useState([]);
  const [loadingPaymentStatus, setLoadingPaymentStatus] = useState(false);
  const [retryingPayment, setRetryingPayment] = useState(false);

  // Fetch orders on component mount or filter change
  useEffect(() => {
    fetchOrders();

    // Check if there's a new order parameter in URL
    const urlParams = new URLSearchParams(window.location.search);
    const newOrderId = urlParams.get("new_order");

    if (newOrderId) {
      // Fetch and show the new order
      fetchOrderDetail(newOrderId);
      setSelectedOrderId(parseInt(newOrderId));

      // Clean up the URL
      window.history.replaceState({}, document.title, window.location.pathname);
    }
  }, [filterStatus]);

  useEffect(() => {
    if (tab) {
      setFilterStatus(tab);
    }
  }, [tab]);

  // Fetch order list from API
  const fetchOrders = async () => {
    setLoading(true);
    try {
      const response = await window.ApiClient.getOrders(filterStatus);
      if (response.orders) {
        setOrders(response.orders);
      } else {
        setOrders([]);
        showNotification("No orders found", "info");
      }
    } catch (error) {
      console.error("Error fetching orders:", error);
      showNotification("Failed to load orders", "error");
      setOrders([]);
    } finally {
      setLoading(false);
    }
  };

  // Fetch specific order detail
  const fetchOrderDetail = async (orderId) => {
    if (!orderId) return;

    setLoadingOrderDetail(true);
    try {
      const response = await window.ApiClient.getOrderDetails(orderId);
      if (response.order) {
        setSelectedOrder(response.order);
        // Also fetch payment transactions for this order
        fetchPaymentTransactions(orderId);
      } else {
        setSelectedOrder(null);
        showNotification("Order not found", "error");
      }
    } catch (error) {
      console.error("Error fetching order detail:", error);
      showNotification("Failed to load order details", "error");
      setSelectedOrder(null);
    } finally {
      setLoadingOrderDetail(false);
    }
  };

  // Fetch payment transactions for an order
  const fetchPaymentTransactions = async (orderId) => {
    try {
      const response = await window.ApiClient.getOrderPaymentTransactions(orderId);
      if (response.success) {
        setPaymentTransactions(response.transactions || []);
      }
    } catch (error) {
      console.error("Error fetching payment transactions:", error);
      setPaymentTransactions([]);
    }
  };

  // Check payment status
  const checkPaymentStatus = async (transactionId) => {
    if (!transactionId) {
      showNotification("No transaction ID available", "error");
      return;
    }

    setLoadingPaymentStatus(true);
    try {
      const response = await window.ApiClient.getPaymentStatus(transactionId);
      if (response.success) {
        showNotification("Payment status updated", "success");
        // Refresh order details and transactions
        if (selectedOrder) {
          fetchOrderDetail(selectedOrder.id);
          fetchOrders(); // Refresh the order list
        }
      } else {
        showNotification(response.message || "Failed to check payment status", "error");
      }
    } catch (error) {
      console.error("Error checking payment status:", error);
      showNotification("Failed to check payment status", "error");
    } finally {
      setLoadingPaymentStatus(false);
    }
  };

  // Retry payment
  const retryPayment = async (orderId) => {
    if (!orderId) {
      showNotification("Order ID not available", "error");
      return;
    }

    setRetryingPayment(true);
    try {
      const response = await window.ApiClient.retryPayment(orderId);
      if (response.success && response.redirectUrl) {
        showNotification("Redirecting to payment gateway...", "info");
        window.location.href = response.redirectUrl;
      } else {
        showNotification(response.message || "Failed to retry payment", "error");
      }
    } catch (error) {
      console.error("Error retrying payment:", error);
      showNotification("Failed to retry payment", "error");
    } finally {
      setRetryingPayment(false);
    }
  };

  // Handle order selection
  const selectOrder = (orderId) => {
    if (orderId === selectedOrderId) {
      // Deselect if already selected
      setSelectedOrderId(null);
      setSelectedOrder(null);
    } else {
      setSelectedOrderId(orderId);
      fetchOrderDetail(orderId);
    }
  };

  // Handle order cancellation
  const handleCancelOrder = async () => {
    if (!selectedOrder || !cancellationReason.trim()) {
      showNotification("Please provide a reason for cancellation", "error");
      return;
    }

    setCancellingOrder(true);
    try {
      const response = await window.ApiClient.cancelOrder(
        selectedOrder.id,
        cancellationReason
      );

      if (response.success) {
        showNotification("Order cancelled successfully", "success");
        // Refresh order data
        fetchOrderDetail(selectedOrder.id);
        fetchOrders();
        setShowCancelModal(false);
      } else {
        throw new Error(response.message || "Failed to cancel order");
      }
    } catch (error) {
      console.error("Error cancelling order:", error);
      showNotification(
        error.message || "Failed to cancel order, please try again",
        "error"
      );
    } finally {
      setCancellingOrder(false);
    }
  };

  // Format date to a readable format
  const formatDate = (dateString) => {
    if (!dateString) return "";
    return formatToIST(dateString);
  };

  // Get appropriate status color
  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case "delivered":
        return "text-green-600 bg-green-50";
      case "processing":
        return "text-blue-600 bg-blue-50";
      case "placed":
        return "text-purple-600 bg-purple-50";
      case "cancelled":
        return "text-red-600 bg-red-50";
      case "out_for_delivery":
      case "out for delivery":
        return "text-yellow-600 bg-yellow-50";
      default:
        return "text-gray-600 bg-gray-50";
    }
  };

  // Get appropriate payment status color
  const getPaymentStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case "paid":
        return "text-green-600 bg-green-50";
      case "pending":
        return "text-yellow-600 bg-yellow-50";
      case "failed":
        return "text-red-600 bg-red-50";
      case "cancelled":
        return "text-gray-600 bg-gray-50";
      default:
        return "text-gray-600 bg-gray-50";
    }
  };

  // Format currency
  const formatCurrency = (amount) => {
    if (!amount && amount !== 0) return "";
    return `₹ ${parseFloat(amount).toFixed(2)}`;
  };

  // Show notification with proper icon
  const showNotification = (message, type = "success") => {
    if (typeof window !== "undefined") {
      let toastContainer = document.querySelector(".toast-container");

      if (!toastContainer) {
        toastContainer = document.createElement("div");
        toastContainer.className =
          "toast-container fixed bottom-24 sm:bottom-20 left-0 right-0 flex flex-col items-center z-50 pointer-events-none px-4";
        document.body.appendChild(toastContainer);
      }

      const toast = document.createElement("div");
      toast.className =
        "bg-gray-800 text-white px-4 py-3 rounded-xl text-sm font-medium shadow-lg opacity-0 transition-all duration-300 transform translate-y-4 mb-2 flex items-center max-w-md w-full justify-center";

      const icon = document.createElement("span");
      icon.className = "material-icons-round mr-2 text-[16px] md:text-[18px]";

      if (type === "error") {
        icon.textContent = "error";
      } else if (type === "success") {
        icon.textContent = "check_circle";
      } else {
        icon.textContent = "info";
      }

      toast.appendChild(icon);

      const textSpan = document.createElement("span");
      textSpan.textContent = message;
      toast.appendChild(textSpan);

      toastContainer.appendChild(toast);

      setTimeout(() => {
        toast.classList.remove("opacity-0", "translate-y-4");
        toast.classList.add("opacity-95");
      }, 10);

      setTimeout(() => {
        toast.classList.add("opacity-0", "translate-y-4");
        setTimeout(() => {
          toast.remove();
        }, 300);
      }, 3000);
    }
  };

  // Render order item
  const renderOrderItem = (item) => (
    <div
      key={item.id}
      className="border-b border-gray-100 pb-3 mb-3 last:border-0 last:pb-0 last:mb-0"
    >
      <div className="flex items-center">
        {item.product_image && (
          <div className="w-12 h-12 rounded-md overflow-hidden bg-gray-100 flex-shrink-0 border border-gray-200">
            <img
              src={item.product_image}
              alt={item.product_name}
              className="w-full h-full object-cover"
            />
          </div>
        )}
        <div className={`flex-1 ${item.product_image ? "ml-3" : ""}`}>
          <div className="flex justify-between">
            <h4 className="text-sm font-medium text-gray-800">
              {item.product_name}
            </h4>
            <span className="text-sm font-semibold text-gray-800">
              {formatCurrency(item.total_price)}
            </span>
          </div>
          <p className="text-xs text-gray-500 mt-1">
            {item.quantity} × {formatCurrency(item.product_price)}
          </p>
        </div>
      </div>
    </div>
  );

  // Cancel order modal
  const renderCancelModal = () => (
    <div
      className={`fixed inset-0 z-50 flex items-center justify-center ${
        showCancelModal ? "" : "hidden"
      }`}
    >
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black opacity-50"
        onClick={() => !cancellingOrder && setShowCancelModal(false)}
      ></div>

      {/* Modal content */}
      <div className="bg-white rounded-xl p-5 w-full max-w-md mx-4 z-10 shadow-xl">
        <h3 className="text-xl font-semibold mb-4 text-gray-800">
          Cancel Order
        </h3>
        <p className="text-gray-600 text-sm mb-4">
          Please provide a reason for cancellation. This will help us improve
          our service.
        </p>

        <textarea
          className="w-full border border-gray-300 rounded-lg p-3 mb-4 focus:outline-none focus:ring-2 focus:ring-blue-500"
          rows="3"
          placeholder="Enter reason for cancellation..."
          value={cancellationReason}
          onChange={(e) => setCancellationReason(e.target.value)}
          disabled={cancellingOrder}
        ></textarea>

        <div className="flex space-x-3">
          <button
            onClick={() => !cancellingOrder && setShowCancelModal(false)}
            className="flex-1 py-3 border border-gray-300 rounded-lg text-gray-700 font-medium hover:bg-gray-50 transition-all"
            disabled={cancellingOrder}
          >
            Cancel
          </button>
          <button
            onClick={handleCancelOrder}
            className="flex-1 py-3 bg-red-600 text-white rounded-lg font-medium hover:bg-red-700 transition-all flex items-center justify-center"
            disabled={cancellingOrder}
          >
            {cancellingOrder ? (
              <>
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                Processing...
              </>
            ) : (
              "Confirm Cancellation"
            )}
          </button>
        </div>
      </div>
    </div>
  );

  // Add handleUPIPayment function at the top of the component
  const handleUPIPayment = (amount) => {
    const merchantUPI = "9392333935@ybl";
    const merchantName = "Sreekar Publishers";
    const transactionNote = "Order payment";

    // Generate universal UPI link that will trigger the native app selector on mobile
    const upiLink = `upi://pay?pa=${merchantUPI}&pn=${encodeURIComponent(
      merchantName
    )}&am=${amount.toFixed(2)}&tn=${encodeURIComponent(transactionNote)}`;

    try {
      // Try to open the UPI app selector
      window.location.href = upiLink;

      // Show notification
      showNotification("Opening UPI apps...", "info");

      // Set a timeout to handle if no UPI apps are available
      setTimeout(() => {
        showNotification(
          "If no UPI app opened, please install a UPI app like PhonePe, Google Pay, or Paytm, or scan the QR code above.",
          "warning"
        );
      }, 3000);
    } catch (error) {
      console.error("Error opening UPI app:", error);
      showNotification(
        "Unable to open UPI apps. Please scan the QR code to pay.",
        "error"
      );
    }
  };

  return (
    <div className="pb-24 sm:pb-16">
      {/* Status filter tabs */}
      <div className="bg-white rounded-xl shadow border border-gray-100 mb-5 p-1.5 flex overflow-x-auto no-scrollbar">
        <button
          onClick={() => setFilterStatus("all")}
          className={`flex-1 min-w-[100px] py-2.5 px-4 rounded-lg text-sm font-medium transition ${
            filterStatus === "all"
              ? "bg-[#5466F7] text-white"
              : "text-gray-500 hover:bg-gray-50"
          }`}
        >
          All Orders
        </button>
        <button
          onClick={() => setFilterStatus("processing")}
          className={`flex-1 min-w-[100px] py-2.5 px-4 rounded-lg text-sm font-medium transition ${
            filterStatus === "processing"
              ? "bg-[#5466F7] text-white"
              : "text-gray-500 hover:bg-gray-50"
          }`}
        >
          Processing
        </button>
        <button
          onClick={() => setFilterStatus("delivered")}
          className={`flex-1 min-w-[100px] py-2.5 px-4 rounded-lg text-sm font-medium transition ${
            filterStatus === "delivered"
              ? "bg-[#5466F7] text-white"
              : "text-gray-500 hover:bg-gray-50"
          }`}
        >
          Delivered
        </button>
        <button
          onClick={() => setFilterStatus("cancelled")}
          className={`flex-1 min-w-[100px] py-2.5 px-4 rounded-lg text-sm font-medium transition ${
            filterStatus === "cancelled"
              ? "bg-[#5466F7] text-white"
              : "text-gray-500 hover:bg-gray-50"
          }`}
        >
          Cancelled
        </button>
      </div>

      {loading ? (
        <div className="flex flex-col items-center justify-center py-16 px-4 text-center">
          <div className="w-16 h-16 border-4 border-gray-100 border-t-[#5466F7] border-b-[#5466F7] rounded-full animate-spin mb-5"></div>
          <p className="text-gray-600 font-medium">Loading your orders...</p>
        </div>
      ) : orders.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-16 px-4 text-center">
          <div className="w-24 h-24 bg-blue-50 rounded-full flex items-center justify-center mb-6 border border-blue-100 shadow-inner">
            <span className="material-icons-round text-[#5466F7] text-[40px]">
              receipt_long
            </span>
          </div>
          <h2 className="text-xl font-semibold text-gray-800 mb-3">
            No orders found
          </h2>
          <p className="text-gray-500 mb-8 max-w-xs">
            You don't have any {filterStatus !== "all" ? filterStatus : ""}{" "}
            orders yet. Browse our educational materials and place your first order!
          </p>
          <a
            href="/"
            className="bg-[#5466F7] text-white px-8 py-3.5 rounded-xl font-medium hover:bg-[#4555e2] transition-all flex items-center justify-center shadow-sm hover:shadow"
          >
            <span className="material-icons-round mr-2">school</span>
            Browse Materials
          </a>
        </div>
      ) : (
        <div className="space-y-4">
          {/* Order list */}
          {orders.map((order) => (
            <div
              key={order.id}
              className={`bg-white rounded-xl shadow border transition-all ${
                selectedOrderId === order.id
                  ? "border-[#5466F7]"
                  : "border-gray-100 hover:border-gray-200"
              }`}
            >
              {/* Order header */}
              <div
                className="p-4 cursor-pointer"
                onClick={() => selectOrder(order.id)}
              >
                <div className="flex justify-between items-start">
                  <div>
                    <div className="flex items-center">
                      <h3 className="font-semibold text-gray-800">
                        #{order.order_number}
                      </h3>
                      <span
                        className={`ml-2 text-xs py-1 px-2.5 rounded-full font-medium ${getStatusColor(
                          order.order_status
                        )}`}
                      >
                        {order.order_status}
                      </span>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      {formatDate(order.created_at)}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-[#5466F7]">
                      {formatCurrency(order.total_amount)}
                    </p>
                    <p
                      className={`text-xs mt-1 py-0.5 px-2 rounded inline-block ${getPaymentStatusColor(
                        order.payment_status
                      )}`}
                    >
                      {order.payment_status}
                    </p>
                  </div>
                </div>
                <div className="flex justify-between items-center mt-3">
                  <p className="text-sm text-gray-600">
                    {order.items?.length || 0} item
                    {order.items?.length !== 1 ? "s" : ""}
                  </p>
                  <span className="material-icons-round text-gray-400 text-lg">
                    {selectedOrderId === order.id
                      ? "keyboard_arrow_up"
                      : "keyboard_arrow_down"}
                  </span>
                </div>
              </div>

              {/* Order details (expanded) */}
              {selectedOrderId === order.id && (
                <div className="border-t border-gray-100">
                  {loadingOrderDetail ? (
                    <div className="p-4 flex justify-center">
                      <div className="w-8 h-8 border-2 border-gray-200 border-t-[#5466F7] rounded-full animate-spin"></div>
                    </div>
                  ) : selectedOrder ? (
                    <div className="p-4">
                      {/* Order Items */}
                      <h4 className="font-medium text-gray-800 mb-3">
                        Order Items
                      </h4>
                      <div className="bg-gray-50 rounded-lg p-3 mb-4">
                        {selectedOrder.items?.map((item) =>
                          renderOrderItem(item)
                        )}
                      </div>

                      {/* Delivery Address */}
                      <h4 className="font-medium text-gray-800 mb-3">
                        Delivery Address
                      </h4>
                      <div className="bg-gray-50 rounded-lg p-3 mb-4">
                        {selectedOrder.address ? (
                          <>
                            <p className="text-sm font-medium text-gray-800">
                              {selectedOrder.address.full_name}
                            </p>
                            <p className="text-sm text-gray-600 mt-1">
                              {selectedOrder.address.phone}
                            </p>
                            <p className="text-sm text-gray-600 mt-1">
                              
                              {selectedOrder.address.zip_code}
                            </p>
                            {selectedOrder.address.district && (
                              <p className="text-sm text-gray-600 mt-1">
                                District: {selectedOrder.address.district}
                              </p>
                            )}
                            {selectedOrder.address.nearest_busstand && (
                              <p className="text-sm text-gray-600 mt-1">
                                Nearest Bus Stand: {selectedOrder.address.nearest_busstand}
                              </p>
                            )}
                            {selectedOrder.address.school_name && (
                              <p className="text-sm text-gray-600 mt-1">
                                School Name: {selectedOrder.address.school_name}
                              </p>
                            )}
                            {selectedOrder.address.whatsapp_number && (
                              <p className="text-sm text-gray-600 mt-1">
                                WhatsApp: {selectedOrder.address.whatsapp_number}
                              </p>
                            )}
                            {selectedOrder.address.instructions && (
                              <p className="text-xs text-gray-500 mt-2 bg-gray-100 p-2 rounded">
                                <span className="font-medium">Note:</span>{" "}
                                {selectedOrder.address.instructions}
                              </p>
                            )}
                          </>
                        ) : (
                          <p className="text-sm text-gray-500">
                            Address not available
                          </p>
                        )}
                      </div>

                      {/* Payment Information */}
                      <h4 className="font-medium text-gray-800 mb-3">
                        Payment Information
                      </h4>
                      <div className="bg-gray-50 rounded-lg p-3 mb-4">
                        <div className="flex justify-between text-sm py-1">
                          <span className="text-gray-600">Payment Method</span>
                          <span className="font-medium text-gray-800 capitalize">
                            {selectedOrder.payment_method}
                          </span>
                        </div>
                        <div className="flex justify-between text-sm py-1">
                          <span className="text-gray-600">Payment Status</span>
                          <span className={`text-xs py-1 px-2 rounded font-medium ${getPaymentStatusColor(selectedOrder.payment_status)}`}>
                            {selectedOrder.payment_status}
                          </span>
                        </div>

                        {/* Payment Transactions */}
                        {paymentTransactions.length > 0 && (
                          <div className="mt-3 pt-3 border-t border-gray-200">
                            <p className="text-sm font-medium text-gray-700 mb-2">Payment Transactions:</p>
                            {paymentTransactions.map((transaction) => (
                              <div key={transaction.id} className="bg-white rounded p-2 mb-2 border border-gray-200">
                                <div className="flex justify-between items-start">
                                  <div className="flex-1">
                                    <p className="text-xs text-gray-600">
                                      <span className="font-medium">Payment ID:</span> {transaction.payment_id}
                                    </p>
                                    <p className="text-xs text-gray-600 mt-1">
                                      <span className="font-medium">Amount:</span> {formatCurrency(transaction.amount)}
                                    </p>
                                    <p className="text-xs text-gray-600 mt-1">
                                      <span className="font-medium">Created:</span> {formatDate(transaction.created_at)}
                                    </p>
                                  </div>
                                  <div className="flex flex-col items-end space-y-1">
                                    <span className={`text-xs py-0.5 px-2 rounded font-medium ${getPaymentStatusColor(transaction.status)}`}>
                                      {transaction.status}
                                    </span>
                                    {/* Check Payment Status Button */}
                                    {(transaction.status === 'initiated' || transaction.status === 'pending') && (
                                      <button
                                        onClick={() => checkPaymentStatus(transaction.payment_id)}
                                        disabled={loadingPaymentStatus}
                                        className="text-xs bg-blue-500 text-white px-2 py-1 rounded hover:bg-blue-600 transition-all disabled:opacity-50 flex items-center"
                                      >
                                        {loadingPaymentStatus ? (
                                          <>
                                            <div className="w-3 h-3 border border-white border-t-transparent rounded-full animate-spin mr-1"></div>
                                            Checking...
                                          </>
                                        ) : (
                                          <>
                                            <span className="material-icons-round text-[12px] mr-1">refresh</span>
                                            Check Status
                                          </>
                                        )}
                                      </button>
                                    )}
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        )}

                        {/* Retry Payment Button for Failed/Pending Online Payments */}
                        {selectedOrder.payment_method === 'online' &&
                         (selectedOrder.payment_status === 'pending' || selectedOrder.payment_status === 'failed') && (
                          <div className="mt-3 pt-3 border-t border-gray-200">
                            <button
                              onClick={() => retryPayment(selectedOrder.id)}
                              disabled={retryingPayment}
                              className="w-full bg-[#5466F7] text-white py-2 px-4 rounded-lg font-medium hover:bg-[#4555e2] transition-all disabled:opacity-50 flex items-center justify-center"
                            >
                              {retryingPayment ? (
                                <>
                                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                                  Processing...
                                </>
                              ) : (
                                <>
                                  <span className="material-icons-round mr-2 text-[18px]">payment</span>
                                  Retry Payment
                                </>
                              )}
                            </button>
                          </div>
                        )}
                      </div>

                      {/* Payment Options for Pending Payments */}
                      {(selectedOrder.payment_status === 'Pending' || selectedOrder.payment_status === 'pending') && selectedOrder.payment_method === 'online' && (
                        <div className="bg-white rounded-xl shadow-md border border-gray-100 p-5 mb-4">
                          <h4 className="font-medium text-gray-800 mb-4 flex items-center">
                            <span className="material-icons-round mr-2 text-[#5466F7]">
                              payments
                            </span>
                            Complete Payment
                          </h4>
                          <div className="space-y-4">
                            {/* QR Code Section */}
                            <div className="bg-gray-50 rounded-xl p-4 border border-blue-100">
                              <div className="text-center">
                                <h4 className="font-semibold text-gray-800 mb-2 flex items-center justify-center">
                                  <span className="material-icons-round text-blue-600 mr-2">
                                    qr_code
                                  </span>
                                  Scan QR Code to Pay
                                </h4>
                                <p className="text-xs text-gray-500 mb-4">
                                  Use any UPI app to scan and pay
                                </p>
                                <div id="qr-code-container" className="flex justify-center mb-4">
                                  <img 
                                    src="https://sreekarpublishers.com/images/sreekar-publishers-qr-code.jpeg"
                                    alt="UPI Payment QR Code" 
                                    className="w-48 h-48 rounded-lg"
                                  />
                                </div>
                                <div className="text-sm text-gray-600 bg-blue-50 p-3 rounded-lg border border-blue-100">
                                  <p className="font-medium text-blue-800 mb-1">
                                    Payment Amount: ₹{selectedOrder.total_amount.toFixed(2)}
                                  </p>
                                  <p className="text-xs text-blue-600">
                                    UPI ID: sreekar9935@fbl
                                  </p>
                                </div>
                              </div>
                            </div>

                            {/* UPI Payment Button */}
                            <div className="bg-white rounded-xl p-4 border border-blue-100">
                              <button
                                onClick={() => handleUPIPayment(selectedOrder.total_amount)}
                                className="w-full flex items-center justify-center p-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-xl hover:from-purple-700 hover:to-blue-700 transition-all shadow-md hover:shadow-lg transform hover:scale-[1.02]"
                              >
                                <div className="flex items-center">
                                  <div className="w-10 h-10 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mr-3">
                                    <span className="material-icons-round text-green-600">
                                      smartphone
                                    </span>
                                  </div>
                                  <div className="text-left">
                                    <p className="text-base font-semibold text-white">
                                      Pay via UPI
                                    </p>
                                  </div>
                                </div>
                                <span className="material-icons-round text-white ml-auto">
                                  chevron_right
                                </span>
                              </button>
                              <p className="text-xs text-gray-500 mt-3 text-center">
                                Clicking this button will show all UPI apps installed on your device
                              </p>
                            </div>
                          </div>
                        </div>
                      )}

                      {/* Price details */}
                      <h4 className="font-medium text-gray-800 mb-3">
                        Price Details
                      </h4>
                      <div className="bg-gray-50 rounded-lg p-3 mb-4">
                        <div className="flex justify-between text-sm py-1">
                          <span className="text-gray-600">Subtotal</span>
                          <span className="font-medium text-gray-800">
                            {formatCurrency(
                              selectedOrder.total_amount -
                                selectedOrder.delivery_fee +
                                selectedOrder.discount_amount
                            )}
                          </span>
                        </div>
                        <div className="flex justify-between text-sm py-1">
                          <span className="text-gray-600">Delivery Fee</span>
                          <span className="font-medium text-gray-800">
                            {formatCurrency(selectedOrder.delivery_fee)}
                          </span>
                        </div>
                        {selectedOrder.discount_amount > 0 && (
                          <div className="flex justify-between text-sm py-1">
                            <span className="text-green-600 flex items-center">
                              <span className="material-icons-round text-[14px] mr-1">
                                local_offer
                              </span>
                              Discount
                              {selectedOrder.coupon_code &&
                                ` (${selectedOrder.coupon_code})`}
                            </span>
                            <span className="font-medium text-green-600">
                              -{formatCurrency(selectedOrder.discount_amount)}
                            </span>
                          </div>
                        )}
                        <div className="border-t border-gray-200 mt-2 pt-2 flex justify-between">
                          <span className="font-medium text-gray-800">
                            Total
                          </span>
                          <span className="font-semibold text-[#5466F7]">
                            {formatCurrency(selectedOrder.total_amount)}
                          </span>
                        </div>
                      </div>

                      {/* Delivery Info */}
                      <h4 className="font-medium text-gray-800 mb-3">
                        Delivery Info
                      </h4>
                      <div className="bg-gray-50 rounded-lg p-3 mb-5">
                        {selectedOrder.location && (
                          <div className="flex justify-between text-sm py-1">
                            <span className="text-gray-600">
                              Delivery Location
                            </span>
                            <span className="font-medium text-gray-800">
                              {selectedOrder.location.name}
                            </span>
                          </div>
                        )}
                        {selectedOrder.delivered_at && (
                          <div className="flex justify-between text-sm py-1">
                            <span className="text-gray-600">Delivered At</span>
                            <span className="font-medium text-gray-800">
                              {formatDate(selectedOrder.delivered_at)}
                            </span>
                          </div>
                        )}
                        {selectedOrder.cancel_reason && (
                          <div className="flex text-sm py-1">
                            <span className="text-gray-600 mr-2">
                              Cancellation Reason:
                            </span>
                            <span className="font-medium text-gray-800">
                              {selectedOrder.cancel_reason}
                            </span>
                          </div>
                        )}
                      </div>

                      {/* Action Buttons */}
                      <div className="flex space-x-3">
                        {/* Only show cancel button if order is in 'placed' or 'processing' status */}
                        {["placed", "processing"].includes(
                          selectedOrder.order_status?.toLowerCase()
                        ) && (
                          <button
                            onClick={() => setShowCancelModal(true)}
                            className="flex-1 py-2.5 border border-red-500 text-red-600 rounded-lg font-medium hover:bg-red-50 transition-all flex items-center justify-center"
                          >
                            <span className="material-icons-round mr-1 text-sm">
                              cancel
                            </span>
                            Cancel Order
                          </button>
                        )}
                      </div>
                    </div>
                  ) : (
                    <div className="p-4 text-center text-gray-500">
                      Order details not available
                    </div>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Cancel order modal */}
      {renderCancelModal()}
    </div>
  );
}
